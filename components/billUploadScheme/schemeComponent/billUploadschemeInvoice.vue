<script setup lang="ts">
// 发票信息组件
import { message } from 'ant-design-vue';
import { onMounted, ref, defineProps, defineEmits, watch, computed, nextTick } from 'vue';
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import arrow from '@/assets/image/orderList/delete.png'; //删除
import add from '@/assets/image/orderList/add.png'; //添加
import { fileApi } from '@haierbusiness-front/apis';
import { UploadFile } from '@haierbusiness-front/common-libs';
import { CurrencyOptions } from '@haierbusiness-front/common-libs';
import { formatNumberThousands } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';

// 关联账单项类型定义
interface RelatedBillItem {
  id: string;
  sequenceNumber: number;
  date: string;
  project: string;
  category: string;
  contractPrice: number;
  contractQuantity: number;
  billPrice: number;
  billQuantity: number;
  relatedBill: string;
}

// 类型定义
interface InvoiceItem {
  tempId: string; // 临时ID
  serialNumber: number; // 序号（不传递给父组件）
  occurDate: Dayjs | string | null; // 时间字段 - 内部使用Dayjs，传递给父组件时转为string
  localCurrency: number | null; // 当地货币
  exchangeRate: number | null; // 汇率
  totalAmountCny: number; // 水单总金额（人民币）
  relatedAmountTotalCny: number; // 关联金额合计
  relatedBill: string;
  paths: string[]; // 附件路径数组
  ratePaths: string[]; // 汇率截图路径数组
  exchangeRateScreenshot?: UploadFile;
  unit: string | null; // 单位字段
  attachmentFiles: UploadFile[]; // 用于UI显示的附件文件对象
  relatedBills?: RelatedBillItem[]; // 关联账单数据
}

// 文件上传相关常量
const SUPPORTED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
const FILE_SIZE_LIMIT = 10; // MB
const UPLOAD_ACCEPT = '.pdf,.jpg,.jpeg,.png,.gif,.doc,.docx';

const props = defineProps({
  invoiceList: {
    type: Array as () => InvoiceItem[],
    default: () => [],
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeType: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  schemeDetail: {
    type: Object as () => any,
    default: () => ({}),
  },
});

const emit = defineEmits(['invoiceEmit', 'viewRelatedBill', 'invoiceDeleted']);

// 计算属性：获取要显示的发票数据
const displayInvoiceList = computed(() => {
  if (props.readonly && props.schemeDetail?.attachmentInvoices) {
    // 查看模式：转换数据格式以匹配组件期望
    const transformedData = props.schemeDetail.attachmentInvoices.map((item: any, index: number) => ({
      ...item,
      tempId: item.tempId || `view_invoice_${item.id || Date.now()}_${index}`, // 添加 tempId
      serialNumber: item.serialNumber || (index + 1), // 添加序号
      localCurrency: item.localCurrency || '', // 确保本币字段存在
      exchangeRate: item.exchangeRate || '', // 确保汇率字段存在
      invoiceAmount: item.invoiceAmount || 0, // 确保发票金额字段存在
      attachments: item.attachments || [], // 确保附件数组存在
      exchangeRateScreenshots: item.exchangeRateScreenshots || [], // 确保汇率截图数组存在
      relatedAmountTotalCny: item.relatedAmountTotalCny || 0, // 关联金额合计
      relatedBills: item.relatedBills || [], // 关联账单
    }));

    return transformedData;
  } else {
    // 编辑模式：使用 invoiceList
    const transformedData = [{
      tempId: `${Date.now()}_0`, // 添加 tempId
      serialNumber: 1, // 添加序号
      localCurrency: '', // 确保本币字段存在
      exchangeRate: '', // 确保汇率字段存在
      invoiceAmount: 0, // 确保发票金额字段存在
      attachments: [], // 确保附件数组存在
      exchangeRateScreenshots: [], // 确保汇率截图数组存在
      relatedAmountTotalCny: 0, // 关联金额合计
      relatedBills: [], // 关联账单
      unit: null, // 确保单位字段无默认值
    }];
    console.log(props.invoiceList,"计算属性props.invoiceList");

    // 🔧 调试：检查关联金额数据
    if (props.invoiceList && props.invoiceList.length > 0) {
      console.log('🔧 发票组件接收到的数据:', props.invoiceList.map(item => ({
        tempId: item.tempId,
        relatedAmountTotalCny: item.relatedAmountTotalCny,
        relatedBill: item.relatedBill,
        relatedBills: item.relatedBills?.length || 0
      })));
    }

    return props.invoiceList || transformedData;
  }
  
});

// 更新指定发票的关联金额合计和关联账单数据
const updateRelatedAmount = (invoiceTempId: string, totalAmount: number, relatedBills?: RelatedBillItem[]) => {
  console.log('🔥 updateRelatedAmount - 更新发票关联金额:', { invoiceTempId, totalAmount, relatedBills });

  const updatedList = props.invoiceList.map((item) => {
    if (item.tempId === invoiceTempId) {
      return {
        ...item,
        relatedAmountTotalCny: totalAmount,
        relatedBills: relatedBills || item.relatedBills || [], // 保存关联账单数据
      };
    }
    return item; // 🔧 修复：必须返回其他未匹配的项目，否则会变成undefined
  });

  emit('invoiceEmit', getInvoiceDataForParent(updatedList));
};

// 锚点跳转函数
const anchorJump = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};

// 校验
const invoiceSub = () => {
  console.log(invoiceListEnd.value, "invoiceListEnd");
  console.log(props.schemeCacheItem.invoices,"props.schemeCacheItem.invoices");
  

  let isVerPassed = true;

  if (!displayInvoiceList.value || displayInvoiceList.value.length ==0 ) {
    isVerPassed = false
    message.error('请添加发票信息')
    return
  }
  if (invoiceListEnd.value || invoiceListEnd.value.length > 0) {
    for (const item of displayInvoiceList.value) {
      if (!item.attachmentFiles || item.attachmentFiles.length == 0) {
        message.error('请上传发票附件')
        anchorJump('invoice' + item.serialNumber);
        isVerPassed = false
        break
      }
      if (!item.occurDate) {
        message.error('请输入时间')
        anchorJump('invoice' + item.serialNumber);
        isVerPassed = false
        break
      }
      if (!item.localCurrency) {
        message.error('请填写当地货币')
        anchorJump('invoice' + item.serialNumber);
        isVerPassed = false
        break
      }
      if (!item.unit) {
        message.error("请填写单位")
        anchorJump('invoice' + item.serialNumber);
        isVerPassed = false
        break
      }
      if (!item.exchangeRate) {
        message.error('请填写汇率')
        anchorJump('invoice' + item.serialNumber);
        isVerPassed = false
        break
      }
    }
  }

  return isVerPassed;
};

// 暴露给父组件的方法
defineExpose({
  getInvoiceDataForSubmit: () => getInvoiceDataForSubmit(props.invoiceList),
  updateRelatedAmount,
  getInvoiceDataForCache:()=> getInvoiceDataForCache(props.invoiceList),
  invoiceSub
});

// 响应式数据
const uploadLoading = ref<Record<string, boolean>>({});
const previewVisible = ref(false);
const previewFile = ref<UploadFile | null>(null);
const previewFileName = ref('');

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 去除文件名中的时间戳前缀
const removeTimestampPrefix = (fileName: string): string => {
  if (!fileName) return '';
  
  // 匹配模式：数字开头，后面跟着连字符，再跟着其他字符
  // 例如：1754300525-见证性材料.png -> 见证性材料.png
  const timestampPattern = /^(\d+)-(.+)$/;
  const match = fileName.match(timestampPattern);
  
  if (match) {
    return match[2]; // 返回连字符后面的部分
  }
  
  return fileName; // 如果没有匹配到模式，返回原文件名
};

// 获取文件显示名称
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '';

  // 去除时间戳前缀（如：1754300525-见证性材料.png -> 见证性材料.png）
  const cleanedFileName = removeTimestampPrefix(fileName);

  const maxLength = 15;
  if (cleanedFileName.length <= maxLength) return cleanedFileName;

  const extension = cleanedFileName.split('.').pop() || '';
  const nameWithoutExt = cleanedFileName.substring(0, cleanedFileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';

  return `${truncatedName}.${extension}`;
};

// 文件上传前验证（用于Ant Design Upload组件）
const beforeUpload = (file: File): boolean => {
  const isValidType =
    SUPPORTED_FILE_TYPES.includes(file.type) ||
    file.name.toLowerCase().endsWith('.pdf') ||
    file.name.toLowerCase().endsWith('.doc') ||
    file.name.toLowerCase().endsWith('.docx');

  if (!isValidType) {
    message.error('只支持上传 PDF、图片、Word 文档格式的文件！');
    return false;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`文件大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return false;
  }

  return false; // 阻止默认上传，使用自定义上传
};

// 文件验证（用于自定义上传）
const validateFile = (file: File): boolean => {
  const isValidType =
    SUPPORTED_FILE_TYPES.includes(file.type) ||
    file.name.toLowerCase().endsWith('.pdf') ||
    file.name.toLowerCase().endsWith('.doc') ||
    file.name.toLowerCase().endsWith('.docx');

  if (!isValidType) {
    message.error('只支持上传 PDF、图片、Word 文档格式的文件！');
    return false;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`文件大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return false;
  }

  return true; // 验证通过
};

// 新增发票
const handleAddInvoice = () => {
  if (props.readonly) {
    return;
  }

  try {
    // 使用时间戳生成唯一标识
    const timestamp = Date.now();
    const randomSuffix = Math.floor(Math.random() * 1000);
    const uniqueId = `invoice_${timestamp}_${randomSuffix}`;

    const newInvoice: InvoiceItem = {
      tempId: uniqueId, // 使用时间戳生成的唯一标识
      serialNumber: props.invoiceList.length + 1, // 序号
      occurDate: null, // 时间字段，初始为空
      localCurrency: null,
      exchangeRate: null,
      totalAmountCny: 0, // 水单总金额
      relatedAmountTotalCny: 0, // 关联金额合计
      relatedBill: '关联>>',
      paths: [], // 附件路径数组，初始为空
      ratePaths: [], // 汇率截图路径数组，初始为空
      unit: null, // 单位字段，初始为空，无默认值
      attachmentFiles: [], // 用于UI显示的附件文件对象，初始为空
      relatedBills: [], // 关联账单数据，初始为空
    };

    const updatedList = [...props.invoiceList, newInvoice];
    emit('invoiceEmit', getInvoiceDataForParent(updatedList));
    // message.success('发票记录创建成功，请上传附件');
  } catch (error) {
    console.error('新增发票出错:', error);
    message.error('新增发票失败，请重试');
  }
};

// 计算水单总金额
const calculateTotalAmountCny = (localCurrency: number | null, exchangeRate: number | null): number => {
  if (
    localCurrency !== null &&
    localCurrency !== undefined &&
    exchangeRate !== null &&
    exchangeRate !== undefined &&
    localCurrency > 0 &&
    exchangeRate > 0
  ) {
    return localCurrency * exchangeRate;
  }
  return 0;
};

// 更新发票的当地货币
const updateInvoiceLocalCurrency = (itemId: string, value: number | null) => {
  const updatedList = props.invoiceList.map((item) => {
    if (item.tempId === itemId) {
      return {
        ...item,
        localCurrency: value,
        totalAmountCny: calculateTotalAmountCny(value, item.exchangeRate),
      };
    }
    return item;
  });
  emit('invoiceEmit', getInvoiceDataForParent(updatedList));
};

// 更新发票的汇率
const updateInvoiceExchangeRate = (itemId: string, value: number | null) => {
  const updatedList = props.invoiceList.map((item) => {
    if (item.tempId === itemId) {
      return {
        ...item,
        exchangeRate: value,
        totalAmountCny: calculateTotalAmountCny(item.localCurrency, value),
      };
    }
    return item;
  });
  emit('invoiceEmit', getInvoiceDataForParent(updatedList));
};

// 更新发票的时间
const updateInvoiceOccurDate = (itemId: string, value: Dayjs | null) => {
  const updatedList = props.invoiceList.map((item) => {
    if (item.tempId === itemId) {
      return {
        ...item,
        occurDate: value,
      };
    }
    return item;
  });
  emit('invoiceEmit', getInvoiceDataForParent(updatedList));
};

// 更新发票的单位
const updateInvoiceUnit = (itemId: string, value: string | null) => {
  const updatedList = props.invoiceList.map((item) => {
    if (item.tempId === itemId) {
      return {
        ...item,
        unit: value,
      };
    }
    return item;
  });
  emit('invoiceEmit', getInvoiceDataForParent(updatedList));
};

// 删除发票记录
const handleDeleteInvoice = (itemId: string) => {
  if (props.readonly) {
    message.warning('查看模式下不能删除发票');
    return;
  }

  // 过滤掉要删除的发票
  const updatedList = props.invoiceList.filter((item) => item.tempId !== itemId);

  // 重新分配序号
  const reorderedList = updatedList.map((item, index) => ({
    ...item,
    serialNumber: index + 1,
  }));

  // 通知父组件发票被删除，需要清除关联数据
  emit('invoiceDeleted', itemId);

  // 更新发票列表
  emit('invoiceEmit', getInvoiceDataForParent(reorderedList));

  message.success('发票删除成功');
};

// 附件上传
const handleAttachmentUpload = (options: any, itemId: string) => {
  if (props.readonly) {
    message.warning('查看模式下不能上传文件');
    return;
  }

  uploadLoading.value[itemId] = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      // 确保路径正确拼接
      const filePath = it.path || '';
      const fullUrl = filePath ? (filePath.startsWith('/') ? baseUrl + filePath : baseUrl + '/' + filePath) : '';

      console.log('文件上传成功，路径:', filePath, '完整URL:', fullUrl);

      // 从路径提取原始文件名
      const originalFileName = filePath.split('/').pop() || options.file.name;
      // 去除时间戳前缀
      const cleanedFileName = removeTimestampPrefix(originalFileName);

      const fileObj: UploadFile = {
        uid: options.file.uid || Date.now().toString(),
        name: cleanedFileName,
        status: 'done',
        url: fullUrl,
        filePath: filePath,
        fileName: cleanedFileName,
      };

      // 创建一个深拷贝的列表以避免引用问题
      const updatedList = JSON.parse(JSON.stringify(props.invoiceList));

      // 找到并更新对应的发票项
      const invoiceToUpdate = updatedList.find((item: InvoiceItem) => item.tempId === itemId);
      if (invoiceToUpdate) {
        // 确保attachmentFiles数组存在
        invoiceToUpdate.attachmentFiles = invoiceToUpdate.attachmentFiles || [];
        // 添加新文件
        invoiceToUpdate.attachmentFiles.push(fileObj);
        // 更新paths数组
        invoiceToUpdate.paths = invoiceToUpdate.paths || [];
        invoiceToUpdate.paths.push(filePath);
      }

      // 发送更新给父组件
      const dataToSend = getInvoiceDataForParent(updatedList);
      console.log('发送到父组件的数据:', dataToSend);
      emit('invoiceEmit', dataToSend);
      message.success('附件上传成功');
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('附件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value[itemId] = false;
    });
};

// 删除附件
const handleRemoveAttachment = (file: UploadFile, itemId: string) => {
  if (props.readonly) {
    message.warning('查看模式下不能删除文件');
    return;
  }
  const updatedList = props.invoiceList.map((item) => {
    if (item.tempId === itemId) {
      const newAttachmentFiles = (item.attachmentFiles || []).filter((f) => f.uid !== file.uid);

      // 从paths中删除对应的路径
      let newPaths = [...(item.paths || [])];
      if (file.filePath) {
        // 标准化路径处理：移除可能存在的baseUrl前缀，确保路径格式一致
        let pathToRemove = file.filePath;

        // 如果filePath包含baseUrl，先移除baseUrl部分
        if (pathToRemove.includes(baseUrl)) {
          pathToRemove = pathToRemove.replace(baseUrl, '');
        }

        // 确保路径以/开头
        if (!pathToRemove.startsWith('/')) {
          pathToRemove = '/' + pathToRemove;
        }

        // 过滤时考虑多种路径格式的匹配
        newPaths = newPaths.filter((path) => {
          // 标准化存储的路径
          let normalizedPath = path;
          if (normalizedPath.includes(baseUrl)) {
            normalizedPath = normalizedPath.replace(baseUrl, '');
          }
          if (!normalizedPath.startsWith('/') && normalizedPath) {
            normalizedPath = '/' + normalizedPath;
          }

          return normalizedPath !== pathToRemove;
        });
      }

      return {
        ...item,
        attachmentFiles: newAttachmentFiles,
        paths: newPaths,
      };
    }
    return item;
  });

  emit('invoiceEmit', getInvoiceDataForParent(updatedList));
  message.success('附件删除成功');
};

// 汇率截图上传
const handleExchangeRateScreenshotUpload = (options: any, itemId: string) => {
  if (props.readonly) {
    message.warning('查看模式下不能上传文件');
    return;
  }

  // 验证文件类型（只允许图片）
  const file = options.file;
  const isValidType = file.type.startsWith('image/');

  if (!isValidType) {
    message.error('汇率截图只支持上传图片格式的文件！');
    return;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`文件大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return;
  }

  uploadLoading.value[itemId] = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      // 确保路径正确拼接
      const filePath = it.path || '';
      const fullUrl = filePath ? (filePath.startsWith('/') ? baseUrl + filePath : baseUrl + '/' + filePath) : '';

      // 从路径提取原始文件名
      const originalFileName = filePath.split('/').pop() || options.file.name;
      // 去除时间戳前缀
      const cleanedFileName = removeTimestampPrefix(originalFileName);

      const fileObj: UploadFile = {
        uid: options.file.uid || Date.now().toString(),
        name: cleanedFileName,
        status: 'done',
        url: fullUrl,
        filePath: filePath,
        fileName: cleanedFileName,
      };

      const updatedList = props.invoiceList.map((item) => {
        if (item.tempId === itemId) {
          return {
            ...item,
            exchangeRateScreenshot: fileObj,
            ratePaths: [filePath], // 更新汇率截图路径数组
          };
        }
        return item;
      });

      emit('invoiceEmit', getInvoiceDataForParent(updatedList));
      message.success('汇率截图上传成功');
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('汇率截图上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value[itemId] = false;
    });
};

// 文件预览
const handlePreviewFile = (file: UploadFile | null, fileName?: string) => {
  if (file) {
    previewFile.value = file;
    previewFileName.value = file.name;
  } else if (fileName) {
    previewFile.value = null;
    previewFileName.value = fileName;
  }
  previewVisible.value = true;
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewFile.value = null;
  previewFileName.value = '';
};

// 查看关联账单
const handleViewRelatedBill = (item: InvoiceItem) => {
  emit('viewRelatedBill', item,"invoice");
};

// 日期格式化
const formatDate = (date: Dayjs | string | null): string | null => {
  if (!date) return null;
  if (typeof date === 'string') return date;
  return date.format('YYYY-MM-DD');
};

// 将字符串日期转换为Dayjs对象
const parseDate = (date: Dayjs | string | null): Dayjs | null => {
  if (!date) return null;
  if (typeof date === 'string') {
    return dayjs(date);
  }
  return date;
};

const invoiceListEnd = ref()

// 过滤序号字段，保留UI字段以便在父组件和子组件之间共享，拼接完整URL传递给父组件
const getInvoiceDataForParent = (invoiceList: InvoiceItem[]) => {
  invoiceListEnd.value = invoiceList
  console.log(invoiceListEnd.value);
  
  return invoiceList.map((item) => {
    // 只过滤序号，保留 attachmentFiles 和 exchangeRateScreenshot 以便在父组件和子组件之间共享
    const { serialNumber, ...rest } = item;

    // 将 paths 数组中的路径拼接上 baseUrl
    const fullPaths = (item.paths || []).map((path) => {
      if (!path) return '';
      // 如果path已经以/开头，直接拼接；否则添加/
      return path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    });

    // 将 ratePaths 数组中的路径拼接上 baseUrl，并重命名为 path
    const fullRatePath = (item.ratePaths || []).map((path) => {
      if (!path) return '';
      // 如果path已经以/开头，直接拼接；否则添加/
      return path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    });
    console.log(item.relatedBills,"item.relatedBills");
    
    return {
      ...rest,
      occurDate: formatDate(item.occurDate),
      paths: fullPaths,
      ratePaths: fullRatePath,
      path: fullRatePath, // 添加 path 字段（ratePaths 的别名）
      // 保留UI字段以便父子组件数据同步
      attachmentFiles: item.attachmentFiles || [],
      exchangeRateScreenshot: item.exchangeRateScreenshot,
      // 🔧 修复：确保 relatedBills 数据不会丢失
      relatedBills: item.relatedBills || [],
    };
  });
};

// 获取用于缓存的发票数据
const getInvoiceDataForCache = (invoiceList: InvoiceItem[]) => {
  console.log(invoiceList,"invoiceList");
  
  return invoiceList.map((item) => {
    // 将 paths 数组中的路径拼接上 baseUrl
    const fullPaths = (item.paths || []).map((path) => {
      if (!path) return '';
      return path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    });

    // 将 ratePaths 数组中的路径拼接上 baseUrl
    const fullRatePath = (item.ratePaths || []).map((path) => {
      if (!path) return '';
      return path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    });

    // 只返回API需要的字段
    // 处理tempId字段，从 "invoice_1753756809308_326" 格式中提取 "1753756809308"
    let processedTempId = item.tempId;
    if (item.tempId && item.tempId.includes('_')) {
      const parts = item.tempId.split('_');
      if (parts.length >= 2) {
        processedTempId = parts[1]; // 提取中间的数字部分
      }
    }

    // 🔧 修复：处理relatedAmountTotalCny字段，支持负数
    let relatedAmountNumber = 0;
    if (item.relatedAmountTotalCny !== null && item.relatedAmountTotalCny !== undefined) {
      // 🔧 修复：支持负数的正则表达式，从 "-1000元" 或 "1000元" 格式中提取数字部分
      const match = item.relatedAmountTotalCny.toString().match(/(-?\d+(?:\.\d+)?)/);
      relatedAmountNumber = match ? parseFloat(match[1]) : 0;

      // 🔧 新增：如果解析失败，尝试直接转换为数字
      if (isNaN(relatedAmountNumber)) {
        relatedAmountNumber = Number(item.relatedAmountTotalCny) || 0;
      }
    }

    return {
      tempId: processedTempId, // 使用处理后的数字ID
      occurDate: formatDate(item.occurDate),
      localCurrency: item.localCurrency,
      unit: item.unit,
      exchangeRate: item.exchangeRate,
      totalAmountCny: item.totalAmountCny,
      relatedAmountTotalCny: relatedAmountNumber, // 转换为数字类型
      relatedBills:item.relatedBills,//缓存需要
      paths: fullPaths,
      path: fullRatePath.length > 0 ? fullRatePath[0] : null, // path是单个字符串，不是数组
    };
  });
};

// 获取用于提交的发票数据（只包含API需要的字段）
const getInvoiceDataForSubmit = (invoiceList: InvoiceItem[]) => {
  console.log(invoiceList,"invoiceList");
  
  return invoiceList.map((item) => {
    // 将 paths 数组中的路径拼接上 baseUrl
    const fullPaths = (item.paths || []).map((path) => {
      if (!path) return '';
      return path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    });

    // 将 ratePaths 数组中的路径拼接上 baseUrl
    const fullRatePath = (item.ratePaths || []).map((path) => {
      if (!path) return '';
      return path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    });

    // 只返回API需要的字段
    // 处理tempId字段，从 "invoice_1753756809308_326" 格式中提取 "1753756809308"
    let processedTempId = item.tempId;
    if (item.tempId && item.tempId.includes('_')) {
      const parts = item.tempId.split('_');
      if (parts.length >= 2) {
        processedTempId = parts[1]; // 提取中间的数字部分
      }
    }

    // 🔧 修复：处理relatedAmountTotalCny字段，支持负数
    let relatedAmountNumber = 0;
    if (item.relatedAmountTotalCny !== null && item.relatedAmountTotalCny !== undefined) {
      // 🔧 修复：支持负数的正则表达式，从 "-1000元" 或 "1000元" 格式中提取数字部分
      const match = item.relatedAmountTotalCny.toString().match(/(-?\d+(?:\.\d+)?)/);
      relatedAmountNumber = match ? parseFloat(match[1]) : 0;

      // 🔧 新增：如果解析失败，尝试直接转换为数字
      if (isNaN(relatedAmountNumber)) {
        relatedAmountNumber = Number(item.relatedAmountTotalCny) || 0;
      }
    }

    return {
      tempId: processedTempId, // 使用处理后的数字ID
      occurDate: formatDate(item.occurDate),
      localCurrency: item.localCurrency,
      unit: item.unit,
      exchangeRate: item.exchangeRate,
      totalAmountCny: item.totalAmountCny,
      relatedAmountTotalCny: relatedAmountNumber, // 转换为数字类型
      paths: fullPaths,
      path: fullRatePath.length > 0 ? fullRatePath[0] : null, // path是单个字符串，不是数组
    };
  });
};

// 下载文件
const handleDownloadFile = () => {
  if (previewFile.value && previewFile.value.url) {
    window.open(previewFile.value.url, '_blank');
  }
};

// 初始化发票数据，实现缓存优先逻辑
const initializeInvoiceData = () => {
  // 查看模式：直接显示数据，不走缓存逻辑
  if (props.readonly) {
    return;
  }

  // 如果有缓存数据，优先使用缓存
  if (
    props.isSchemeCache &&
    props.schemeCacheItem &&
    props.schemeCacheItem.invoices &&
    props.schemeCacheItem.invoices.length > 0
  ) {
    const cacheInvoices = props.schemeCacheItem.invoices;

    const processedCacheData = cacheInvoices.map((item: any, index: number) => {
      const processedItem = {
        ...item,
        serialNumber: index + 1,
        attachmentFiles: item.attachmentFiles || [],
        occurDate: parseDate(item.occurDate),
      };

      // 处理缓存中的附件数据
      if (item.paths && item.paths.length > 0 && (!item.attachmentFiles || item.attachmentFiles.length === 0)) {
        processedItem.attachmentFiles = item.paths.map((path: string, pathIndex: number) => {
          let processedPath = path;
          if (path.startsWith('http') && baseUrl) {
            processedPath = path.replace(new RegExp(`^${baseUrl}`), '');
          }

          const fullUrl = processedPath.startsWith('http')
            ? processedPath
            : processedPath.startsWith('/')
              ? baseUrl + processedPath
              : baseUrl + '/' + processedPath;

          // 从路径提取原始文件名
          const originalFileName = processedPath.split('/').pop() || `附件${pathIndex + 1}`;
          // 去除时间戳前缀
          const cleanedFileName = removeTimestampPrefix(originalFileName);

          return {
            uid: `${item.tempId || Date.now()}_${pathIndex}`,
            name: cleanedFileName,
            status: 'done' as const,
            url: fullUrl,
            filePath: processedPath,
            fileName: cleanedFileName,
          };
        });
      }

      return processedItem;
    });

    emit('invoiceEmit', getInvoiceDataForParent(processedCacheData));
    return;
  }
};

// 监听缓存状态和数据变化
watch(
  () => [props.isSchemeCache, props.schemeCacheItem, props.invoiceList],
  () => {
    initializeInvoiceData();
  },
  { deep: true, immediate: true },
);

// 监听invoiceList的变化，重新计算序号（仅在非缓存模式下）
watch(
  () => props.invoiceList,
  (newVal) => {
    if (!props.isSchemeCache && newVal && newVal.length > 0) {
      // 重新分配序号
      const updatedList = newVal.map((item, index) => ({
        ...item,
        serialNumber: index + 1,
      }));

      // 只更新本地显示，不触发向父组件的事件
      props.invoiceList.forEach((item, index) => {
        if (updatedList[index]) {
          item.serialNumber = updatedList[index].serialNumber;
        }
      });
    }
  },
  { deep: true },
);

// 监听 schemeDetail 变化（仅查看模式使用）
watch(() => props.schemeDetail, (newSchemeDetail) => {
  // 只在查看模式下处理 schemeDetail
  if (props.readonly && newSchemeDetail && newSchemeDetail.attachmentInvoices) {
    // 查看模式：直接使用数据展示，通过 emit 传递给父组件进行显示
    emit('invoiceEmit', newSchemeDetail.attachmentInvoices);
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 初始化数据（仅编辑模式）
  if (!props.readonly) {
    initializeInvoiceData();
  }
  nextTick(() => {
    handleAddInvoice()
  })
});


</script>

<template>
  <!-- 发票信息 -->
  <div class="invoice-section">
    <div class="contract_title">
      <div class="interact_shu mr20"></div>
      <span>发票信息</span>
    </div>
    <div class="info-table-wrapper invoice-table">
      <div class="table-header">
        <div class="col-serial font-color">序号</div>
        <div class="col-attachment font-color">附件</div>
        <div class="col-date font-color">时间</div>
        <div class="col-currency font-color">当地货币</div>
        <div class="col-unit font-color">单位</div>
        <div class="col-rate font-color">汇率</div>
        <div class="col-amount font-color">发票总金额（人民币）</div>
        <div class="col-related-amount font-color">关联金额合计</div>
        <div class="col-related-bill font-color">关联账单</div>
        <div class="col-operation font-color" v-if="!readonly">操作</div>
      </div>
      <div class="table-body">
        <div v-for="(item, index) in displayInvoiceList" :key="item.tempId || item.id || index" class="table-row" :id="'invoice'+item.serialNumber">
          <!-- 序号 -->
          <div class="col-serial">
            {{ item.serialNumber }}
          </div>

          <!-- 附件 -->
          <div class="col-attachment">
            <div class="attachment-content">
              <!-- 已上传文件标签 -->
              <div class="file-tags" v-if="item.attachmentFiles && item.attachmentFiles.length > 0">
                <a-tag v-for="file in item.attachmentFiles" :key="file.uid" :closable="!readonly" class="file-tag"
                  @click="() => handlePreviewFile(file)" @close="() => handleRemoveAttachment(file, item.tempId)">
                  {{ getFileDisplayName(file.name) }}
                </a-tag>
              </div>
              <div v-else-if="readonly" class="readonly-text">暂无附件</div>

              <!-- 上传按钮 - 查看模式下隐藏 -->
              <a-upload v-if="!readonly" :file-list="[]"
                :custom-request="(options: any) => handleAttachmentUpload(options, item.tempId)" :multiple="true"
                :show-upload-list="false" :accept="UPLOAD_ACCEPT">
                <a-button size="small" type="link" :loading="uploadLoading[item.tempId] || false">
                  <upload-outlined />
                  上传
                </a-button>
              </a-upload>
            </div>
          </div>

          <div class="col-date">
            <!-- 查看模式：显示纯文本 -->
            <span v-if="readonly" class="readonly-text">
              {{
                item.occurDate ? (typeof item.occurDate === 'string' ? item.occurDate :
                  item.occurDate.format('YYYY-MM-DD')) : '-'
              }}
            </span>
            <!-- 编辑模式：显示日期选择器 -->
            <a-date-picker v-else :value="parseDate(item.occurDate)" placeholder="请输入" size="small"
              class="borderless-input" :bordered="false"
              @change="(date: Dayjs | null) => updateInvoiceOccurDate(item.tempId, date)" />
          </div>
          <div class="col-currency">
            <!-- 查看模式：显示纯文本 -->
            <span v-if="readonly" class="readonly-text">
              {{ item.localCurrency ? formatNumberThousands(item.localCurrency) : '-' }}
            </span>
            <!-- 编辑模式：显示数字输入框 -->
            <a-input-number v-else v-model:value="item.localCurrency" :min="0" :precision="2" placeholder="请输入"
              size="small" class="borderless-input" :bordered="false"
              @change="(value: number | null) => updateInvoiceLocalCurrency(item.tempId, value)" />
          </div>
          <div class="col-unit">
            <!-- 查看模式：显示纯文本 -->
            <span v-if="readonly" class="readonly-text">
              {{ item.unit || '-' }}
            </span>
            <!-- 编辑模式：显示下拉选择框 -->
            <a-select v-else v-model:value="item.unit" placeholder="请选择" size="small" class="borderless-input"
              :bordered="false" allow-clear :options="CurrencyOptions"
              @change="(value: string | null) => updateInvoiceUnit(item.tempId, value)" />
          </div>
          <div class="col-rate">
            <div class="rate-content">
              <!-- 查看模式：显示纯文本 -->
              <template v-if="readonly">
                <span class="readonly-text">{{ item.exchangeRate || '-' }}</span>
                <a-tag v-if="item.exchangeRateScreenshot" class="file-tag screenshot-tag"
                  @click="() => handlePreviewFile(item.exchangeRateScreenshot!, '汇率截图')">
                  汇率截图
                </a-tag>
                <span v-else class="readonly-text">无截图</span>
              </template>
              <!-- 编辑模式：显示输入框和上传按钮 -->
              <template v-else>
                <a-input-number v-model:value="item.exchangeRate" :min="0" :precision="4" placeholder="请输入" size="small"
                  class="borderless-input" :bordered="false"
                  @change="(value: number | null) => updateInvoiceExchangeRate(item.tempId, value)" />
                <a-tag v-if="item.exchangeRateScreenshot" class="file-tag screenshot-tag"
                  @click="() => handlePreviewFile(item.exchangeRateScreenshot!, '汇率截图')">
                  汇率截图
                </a-tag>
                <a-upload :file-list="[]"
                  :custom-request="(options: any) => handleExchangeRateScreenshotUpload(options, item.tempId)"
                  :show-upload-list="false" :accept="'image/*'">
                  <a-button size="small" type="link" class="screenshot-btn">
                    {{ item.exchangeRateScreenshot ? '重新上传' : '上传截图' }}
                  </a-button>
                </a-upload>
              </template>
            </div>
          </div>
          <div class="col-amount">{{ item.totalAmountCny > 0 ? formatNumberThousands(item.totalAmountCny) + '元' : '0元' }}</div>
          <div class="col-related-amount">
            <!-- 🔧 修复：改进显示逻辑，确保0也能正确显示，并添加调试信息 -->
            {{ item.relatedAmountTotalCny !== null && item.relatedAmountTotalCny !== undefined
                ? formatNumberThousands(item.relatedAmountTotalCny) + '元'
                : '-' }}
            <!-- 🔧 调试：临时显示原始值，帮助排查问题 -->
            <span v-if="true" style="font-size: 10px; color: #999; display: block;">
              原始值: {{ item.relatedAmountTotalCny }}
            </span>
          </div>
          <div class="col-related-bill">
            <a-button type="link" size="small" @click="() => handleViewRelatedBill(item)">
              {{ item.relatedBill }}
            </a-button>
          </div>

          <!-- 操作列 - 仅编辑模式显示 -->
          <div class="col-operation" v-if="!readonly">
            <a-popconfirm title="确认删除该发票吗？" description="删除后将清除该发票的所有信息及关联数据" ok-text="删除" cancel-text="取消"
              @confirm="() => handleDeleteInvoice(item.tempId)">
              <a-button type="link" size="small" danger>
                <img :src="arrow" alt="" class="imgBig">
              </a-button>
            </a-popconfirm>
          </div>
        </div>

        <!-- 添加按钮行 - 查看模式下隐藏 -->
        <div v-if="!readonly" class="table-row add-row">
          <div class="add-button-full-width" @click="handleAddInvoice">
            <div class="demand_add">
              <img :src="add" alt="" class="imgAddBig" style="margin-right: 5px;">
              <span>新增发票</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal v-model:open="previewVisible" title="文件预览" :footer="null" width="80%" @cancel="handlePreviewCancel">
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewFileName }}</h4>
        </div>
        <div class="preview-body">
          <template v-if="previewFile && previewFile.url">
            <img v-if="
              previewFile.name &&
              (previewFile.name.toLowerCase().includes('.jpg') ||
                previewFile.name.toLowerCase().includes('.jpeg') ||
                previewFile.name.toLowerCase().includes('.png') ||
                previewFile.name.toLowerCase().includes('.gif'))
            " :src="previewFile.url" alt="预览图片" style="max-width: 100%; max-height: 500px; object-fit: contain" />
            <iframe v-else-if="previewFile.name && previewFile.name.toLowerCase().includes('.pdf')"
              :src="previewFile.url" style="width: 100%; height: 500px; border: none"></iframe>
            <div v-else class="file-download">
              <p>无法预览此文件类型，请下载查看</p>
              <a-button type="primary" @click="handleDownloadFile"> 下载文件</a-button>
            </div>
          </template>
          <template v-else>
            <div class="no-file">
              <p>文件信息：{{ previewFileName }}</p>
              <p>暂无可预览的文件内容</p>
            </div>
          </template>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.imgBig {
  width: 20px;
  height: 20px;
}

.imgAddBig {
  width: 16px;
  height: 16px;
}

.invoice-section {
  padding-top: 24px;
  position: relative;
  margin-bottom: 24px;

  .interact_shu {
    width: 4px;
    height: 20px;
    background: #1868db;
    border-radius: 2px;
  }

  .contract_title {
    margin-bottom: 20px;
    font-size: 18px;
    color: #333;
    display: flex;
    font-family: PingFangSC, PingFang SC;
    align-items: center;

    span {
      font-size: 18px;
      color: #1d2129;
    }

    .tip-text {
      font-size: 12px;
      color: #ff4d4f;
      font-weight: normal;
    }
  }

  .info-table-wrapper {
    width: 70%;
    border: none;
    border-bottom: 1px solid #d9d9d9;
    border-radius: 0;
    margin-bottom: 0;
    padding: 0px 30px 30px;

    &.invoice-table {
      width: 100%;
    }

    .table-header {
      display: flex;
      background-color: #f2f3f5;
      font-weight: 500;
      font-size: 14px;
      color: #333;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

      >div {
        padding: 12px 8px;
        text-align: center;
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .font-color {
        color: #86909C;
      }

      .col-serial {
        width: 80px;
      }

      .col-attachment {
        width: 250px;
      }

      .col-date {
        width: 180px;
      }

      .col-currency {
        width: 150px;
      }

      .col-unit {
        width: 100px;
      }

      .col-rate {
        width: 220px;
      }

      .col-amount {
        width: 180px;
      }

      .col-related-amount {
        width: 140px;
      }

      .col-related-bill {
        width: 120px;
      }

      .col-operation {
        width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #e5e6eb;

        >div {
          padding: 12px 8px;
          display: flex;
          align-items: center;
          min-height: 38px;

          &.col-operation {
            justify-content: center;
            align-items: center;
            padding: 8px;
          }
        }

        &:last-child {
          border-bottom: none;
        }

        &.add-row {
          border-bottom: none;

          .add-button-full-width {
            width: 100%;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            min-height: 38px;
            cursor: pointer;
            border-bottom: none;

            &:hover {
              background-color: #f5f5f5;
            }

            .demand_add {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              color: #1890ff;
              font-size: 14px;
              margin-left: 8px;

              .demand_add_img {
                width: 16px;
                height: 16px;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+') no-repeat center;
                background-size: contain;
                margin-right: 8px;
              }
            }
          }
        }

        >div {
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 38px;
          height: 38px;
        }

        .col-serial {
          width: 80px;
        }

        .col-attachment {
          width: 250px;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          gap: 4px;

          .attachment-content {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 4px;
            flex-wrap: wrap;
            justify-content: flex-start;

            .file-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
              justify-content: flex-start;
              max-width: 100%;

              .file-tag {
                cursor: pointer;
                font-size: 10px;
                background-color: #e6f7ff;
                border-color: #1890ff;
                color: #1890ff;
                padding: 2px 4px;
                margin: 1px;

                &:hover {
                  opacity: 0.8;
                  background-color: #bae7ff;
                }
              }
            }
          }
        }

        .col-date {
          width: 180px;
        }

        .col-currency {
          width: 150px;
        }

        .col-unit {
          width: 100px;
        }

        .col-rate {
          width: 220px;
          flex-direction: column;
          align-items: center;
          gap: 8px;

          .rate-content {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .screenshot-tag {
              font-size: 10px;
              padding: 2px 6px;
              cursor: pointer;
            }

            .screenshot-btn {
              font-size: 10px;
              padding: 0;
              height: auto;
              line-height: 1;
            }
          }
        }

        .col-amount {
          width: 180px;
        }

        .col-related-amount {
          width: 140px;
        }

        .col-related-bill {
          width: 120px;
        }

        .col-operation {
          width: 100px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 12px 8px;
        }

        .col-operation {
          width: 100px;
          display: flex;
          align-items: center;
          justify-content: center;

          .ant-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4px;
            height: 32px;
            width: 32px;
            min-width: 32px;
            border-radius: 4px;

            :deep(.anticon) {
              font-size: 16px;
              line-height: 1;
              margin: 0;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .ant-popconfirm {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
          }
        }

        .file-tag {
          cursor: pointer;
          font-size: 12px;
          background-color: #e6f7ff;
          border-color: #1890ff;
          color: #1890ff;
          padding: 4px 8px;

          &:hover {
            opacity: 0.8;
            background-color: #bae7ff;
          }
        }
      }
    }
  }

  // 无边框输入框样式
  .borderless-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input {
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;
    }

    .ant-picker-input>input {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .file-download {
        padding: 40px 0;

        p {
          margin-bottom: 16px;
          color: #666;
        }
      }

      .no-file {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }
}

.mr8 {
  margin-right: 8px;
}

// 查看模式纯文本样式
.readonly-text {
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  padding: 4px 0;
  display: inline-block;
  width: 100%;
  text-align: center;

  &.small {
    font-size: 12px;
    color: #999;
  }
}

// 全局样式覆盖
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}
</style>
